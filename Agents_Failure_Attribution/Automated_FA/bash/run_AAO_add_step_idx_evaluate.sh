#!/bin/bash

# Script to evaluate multi-round data in outputs_new/AAO/add_step_idx
# This script processes both with_gt and without_gt data across multiple rounds (1-4)
# and determines the correct data_path based on handcrafted vs algorithm-generated files

echo "Starting multi-round evaluation for AAO add_step_idx data..."

# Base directory for the multi-round outputs
BASE_DIR="../outputs_new/AAO/add_step_idx"

# Function to determine data_path based on filename
determine_data_path() {
    local filename="$1"
    if [[ $(basename "$filename") == *"handcrafted"* ]]; then
        echo "../../Who&When/Hand-Crafted"
    elif [[ $(basename "$filename") == *"alg_generated"* ]]; then
        echo "../../Who&When/Algorithm-Generated"
    else
        echo "Warning: Cannot determine data path for $filename, using default" >&2
        echo "../../Who&When/Algorithm-Generated"
    fi
}

# Function to evaluate files in a directory
evaluate_directory() {
    local dir="$1"
    local gt_type="$2"
    
    if [ -d "$dir" ]; then
        echo "Processing $gt_type directory: $dir"

        # Check if there are any .txt files
        txt_files=("$dir"/*.txt)
        if [ -e "${txt_files[0]}" ]; then
            echo "Found .txt files in directory"
        else
            echo "No .txt files found in directory"
            return
        fi

        for file in "$dir"/*.txt; do
            if [ -f "$file" ] && [[ ! $(basename "$file") == evaluation_* ]]; then
                echo "Evaluating: $file"

                # Determine data_path based on filename
                data_path=$(determine_data_path "$file")

                echo "  Using data_path: $data_path"

                # Run evaluation
                python3 ../evaluate.py --eval_file "$file" --data_path "$data_path"

                if [ $? -eq 0 ]; then
                    echo "  ✅ Evaluation completed successfully for $file"
                else
                    echo "  ❌ Evaluation failed for $file"
                fi
                echo ""
            else
                echo "Skipping file: $file (not a regular file or evaluation file)"
            fi
        done
    else
        echo "Directory not found: $dir"
    fi
}

# Process with_gt data
echo "=== Processing WITH_GT data ==="
for round_num in {1..4}; do
    echo "--- Round $round_num ---"
    with_gt_dir="$BASE_DIR/with_gt/$round_num"
    evaluate_directory "$with_gt_dir" "with_gt/round_$round_num"
done

echo ""

# Process without_gt data
echo "=== Processing WITHOUT_GT data ==="
for round_num in {1..4}; do
    echo "--- Round $round_num ---"
    without_gt_dir="$BASE_DIR/without_gt/$round_num"
    evaluate_directory "$without_gt_dir" "without_gt/round_$round_num"
done

echo ""
echo "🎉 All multi-round evaluations completed!"

# Summary
echo ""
echo "=== EVALUATION SUMMARY ==="
echo "Processed directories:"
echo "  - with_gt: rounds 1-4"
echo "  - without_gt: rounds 1-4"
echo ""
echo "Data paths used:"
echo "  - Handcrafted files: ../../Who&When/Hand-Crafted"
echo "  - Algorithm-generated files: ../../Who&When/Algorithm-Generated"
echo ""
echo "Evaluation script: ../evaluate.py"
echo "Base directory: $BASE_DIR"
